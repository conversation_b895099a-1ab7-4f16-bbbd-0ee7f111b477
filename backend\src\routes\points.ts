import { Router } from 'express';
import { getPointsStats, getPointsTransactions } from '@/controllers/pointsController';
import { authenticateToken } from '@/middleware/auth';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     PointsStats:
 *       type: object
 *       properties:
 *         total_points:
 *           type: integer
 *           description: 当前积分余额
 *         earned_this_month:
 *           type: integer
 *           description: 本月获得积分
 *         spent_this_month:
 *           type: integer
 *           description: 本月消费积分
 *         earned_this_week:
 *           type: integer
 *           description: 本周获得积分
 *         spent_this_week:
 *           type: integer
 *           description: 本周消费积分
 *         earned_today:
 *           type: integer
 *           description: 今日获得积分
 *         spent_today:
 *           type: integer
 *           description: 今日消费积分
 *         total_earned:
 *           type: integer
 *           description: 累计获得积分
 *     
 *     PointTransaction:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 交易ID
 *         user_id:
 *           type: integer
 *           description: 用户ID
 *         type:
 *           type: string
 *           enum: [earn, spend]
 *           description: 交易类型
 *         amount:
 *           type: integer
 *           description: 积分数量
 *         description:
 *           type: string
 *           description: 交易描述
 *         related_task_id:
 *           type: integer
 *           description: 关联任务ID
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 */

/**
 * @swagger
 * /points/stats:
 *   get:
 *     summary: 获取积分统计
 *     tags: [积分]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/PointsStats'
 *       401:
 *         description: 未登录
 */
router.get('/stats', authenticateToken, getPointsStats);

/**
 * @swagger
 * /points/transactions:
 *   get:
 *     summary: 获取积分交易记录
 *     tags: [积分]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [earn, spend]
 *         description: 交易类型过滤
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     transactions:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/PointTransaction'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         pages:
 *                           type: integer
 *       401:
 *         description: 未登录
 */
router.get('/transactions', authenticateToken, getPointsTransactions);

export default router;
