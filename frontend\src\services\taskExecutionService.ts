import { TaskType, TaskExecution, ExecutionStatus } from '@/types';
import api from './api';
import { taskVerificationService, automatedDetectionService, VerificationEvidence, VerificationResult } from './taskVerificationService';
import { behaviorTrackingService } from './behaviorTrackingService';

export interface TaskExecutionSession {
  id: string;
  taskId: number;
  selectedRequirements: TaskType[];
  windowRef: Window | null;
  startTime: number;
  minDuration: number; // 最小停留时间（毫秒）
  status: 'pending' | 'monitoring' | 'completed' | 'failed' | 'timeout';
  platform?: string; // 平台信息
  isTrackingBehavior?: boolean; // 是否正在跟踪行为
}

export interface ExecutionResult {
  success: boolean;
  completedRequirements: TaskType[];
  failedRequirements: TaskType[];
  pointsEarned: number;
  message: string;
  needsManualReview?: boolean;
  proofRequired?: boolean;
}

export interface ExecutionProof {
  screenshots?: File[];
  description?: string;
  timestamp: number;
}

class TaskExecutionService {
  private activeSessions = new Map<string, TaskExecutionSession>();
  private readonly MIN_TASK_DURATION = 3000; // 3秒最小停留时间（降低要求）
  private readonly TRUSTED_USER_MIN_DURATION = 2000; // 信誉用户2秒最小时间
  private readonly MAX_SESSION_TIMEOUT = 300000; // 5分钟超时
  private readonly QUICK_COMPLETION_THRESHOLD = 5000; // 5秒内完成视为快速完成

  /**
   * 获取用户最小执行时间（基于信誉度）
   */
  private getMinDurationForUser(userReputationScore?: number): number {
    // 如果用户信誉度高于70分，使用更宽松的时间要求
    if (userReputationScore && userReputationScore >= 70) {
      return this.TRUSTED_USER_MIN_DURATION;
    }
    return this.MIN_TASK_DURATION;
  }

  /**
   * 检查是否为可信用户
   */
  private isTrustedUser(userReputationScore?: number): boolean {
    return userReputationScore !== undefined && userReputationScore >= 70;
  }

  /**
   * 检测视频平台
   */
  private detectPlatform(videoUrl: string): string {
    const url = videoUrl.toLowerCase();
    if (url.includes('bilibili.com') || url.includes('b23.tv')) {
      return 'bilibili';
    } else if (url.includes('douyin.com')) {
      return 'douyin';
    } else if (url.includes('kuaishou.com')) {
      return 'kuaishou';
    } else if (url.includes('xiaohongshu.com')) {
      return 'xiaohongshu';
    }
    return 'unknown';
  }

  /**
   * 初始化行为跟踪
   */
  private initializeBehaviorTracking(session: TaskExecutionSession): void {
    // 等待窗口加载后开始跟踪
    setTimeout(() => {
      try {
        if (session.windowRef && !session.windowRef.closed) {
          behaviorTrackingService.startTracking(session.windowRef);
          session.isTrackingBehavior = true;
        }
      } catch (error) {
        console.warn('无法启动行为跟踪:', error);
        // 跟踪失败不影响任务执行
      }
    }, 2000); // 等待2秒让页面加载
  }

  /**
   * 初始化自动检测
   */
  private initializeAutomatedDetection(session: TaskExecutionSession): void {
    setTimeout(() => {
      try {
        if (session.windowRef && !session.windowRef.closed && session.platform) {
          automatedDetectionService.injectDetectionScript(
            session.windowRef,
            session.platform,
            session.id
          );
          console.log(`自动检测脚本已注入到 ${session.platform} 平台`);
        }
      } catch (error) {
        console.warn('注入自动检测脚本失败:', error);
      }
    }, 3000); // 等待3秒让页面完全加载
  }

  /**
   * 开始任务执行会话
   */
  async startExecution(
    taskId: number,
    videoUrl: string,
    selectedRequirements: TaskType[],
    userReputationScore?: number
  ): Promise<TaskExecutionSession> {
    const sessionId = this.generateSessionId();

    // 检测平台
    const platform = this.detectPlatform(videoUrl);

    // 打开新窗口
    const windowRef = window.open(videoUrl, '_blank', 'width=1200,height=800');

    if (!windowRef) {
      throw new Error('无法打开任务窗口，请检查浏览器弹窗设置');
    }

    const minDuration = this.getMinDurationForUser(userReputationScore);
    const session: TaskExecutionSession = {
      id: sessionId,
      taskId,
      selectedRequirements,
      windowRef,
      startTime: Date.now(),
      minDuration,
      status: 'monitoring',
      platform,
      isTrackingBehavior: false
    };

    this.activeSessions.set(sessionId, session);

    // 开始监控窗口状态
    this.monitorWindow(session);

    // 尝试开始行为跟踪（需要等待窗口加载）
    this.initializeBehaviorTracking(session);

    // 注入自动检测脚本
    this.initializeAutomatedDetection(session);

    // 设置超时处理
    setTimeout(() => {
      this.handleSessionTimeout(sessionId);
    }, this.MAX_SESSION_TIMEOUT);

    return session;
  }

  /**
   * 监控窗口状态
   */
  private monitorWindow(session: TaskExecutionSession): void {
    const checkInterval = setInterval(() => {
      if (!session.windowRef || session.windowRef.closed) {
        clearInterval(checkInterval);
        this.handleWindowClosed(session);
      }
    }, 1000);
  }

  /**
   * 处理窗口关闭事件
   */
  private async handleWindowClosed(session: TaskExecutionSession): Promise<void> {
    const duration = Date.now() - session.startTime;
    const durationSeconds = Math.round(duration / 1000);
    const minDurationSeconds = Math.round(session.minDuration / 1000);

    // 检查是否过短
    if (duration < session.minDuration) {
      // 如果时间非常短（小于1秒），可能是意外关闭
      if (duration < 1000) {
        session.status = 'failed';
        this.showCompletionDialog(session, {
          success: false,
          completedRequirements: [],
          failedRequirements: session.selectedRequirements,
          pointsEarned: 0,
          message: `窗口关闭过快（${durationSeconds}秒），可能是意外关闭。请重新打开任务窗口完成操作。`,
          needsManualReview: false
        });
        return;
      }

      // 时间稍短但可能是快速完成，提供申诉选项
      session.status = 'failed';
      this.showCompletionDialog(session, {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: `任务时间较短（${durationSeconds}秒，建议至少${minDurationSeconds}秒）。如果您已完成操作，可以申请人工审核。`,
        needsManualReview: true,
        proofRequired: true
      });
      return;
    }

    session.status = 'completed';
    
    // 执行任务验证
    try {
      const result = await this.verifyTaskCompletion(session);
      this.showCompletionDialog(session, result);
    } catch (error) {
      console.error('任务验证失败:', error);
      this.showCompletionDialog(session, {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: '任务验证失败，请稍后重试或联系客服',
        needsManualReview: true
      });
    }
  }

  /**
   * 验证任务完成情况
   */
  private async verifyTaskCompletion(session: TaskExecutionSession): Promise<ExecutionResult> {
    const duration = Date.now() - session.startTime;
    const durationSeconds = Math.round(duration / 1000);

    // 检查执行时间是否在合理范围内
    if (duration > this.MAX_SESSION_TIMEOUT) {
      return {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: '任务执行时间过长，可能存在异常，请重新完成'
      };
    }

    // 停止行为跟踪并收集数据
    let behaviorData = null;
    if (session.isTrackingBehavior) {
      try {
        behaviorData = behaviorTrackingService.getCurrentData();
        behaviorTrackingService.stopTracking();
      } catch (error) {
        console.warn('获取行为数据失败:', error);
      }
    }

    // 严格验证：必须有行为数据才能进行验证
    if (!behaviorData || !behaviorData.metrics) {
      return {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: '无法获取用户行为数据，验证失败。请确保在任务窗口中实际执行了所需操作。',
        proofRequired: true,
        needsManualReview: true
      };
    }

    // 基础行为验证：必须有最基本的用户交互
    const hasBasicInteraction = behaviorData.metrics.clickCount > 0 && behaviorData.metrics.mouseMovements > 0;
    if (!hasBasicInteraction) {
      return {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: '未检测到基本用户交互（点击和鼠标移动），验证失败。请确保实际执行了所需操作。',
        proofRequired: true,
        needsManualReview: true
      };
    }

    // 获取自动检测结果
    const detectionResults = automatedDetectionService.getDetectionResults(session.id);

    // 使用综合验证系统（增强版 - 包含自动检测）
    if (session.platform && session.platform !== 'unknown') {
      try {
        const evidence: VerificationEvidence = {
          behaviorMetrics: behaviorData.metrics,
          userInteractions: behaviorData.interactions,
          domChanges: behaviorData.domChanges
        };

        // 如果有自动检测结果，优先使用
        if (detectionResults && detectionResults.detectedActions.length > 0) {
          console.log('使用自动检测结果:', detectionResults);
          return this.processAutomatedDetectionResults(session, detectionResults, durationSeconds);
        }

        // 否则使用传统验证方法
        const verificationResults = await taskVerificationService.verifyTaskCompletion(
          session.platform,
          session.selectedRequirements,
          evidence,
          session.id
        );

        return this.processVerificationResults(session, verificationResults, durationSeconds);
      } catch (error) {
        console.error('综合验证失败:', error);
        return {
          success: false,
          completedRequirements: [],
          failedRequirements: session.selectedRequirements,
          pointsEarned: 0,
          message: '验证系统出现错误，请稍后重试或联系客服。',
          proofRequired: true,
          needsManualReview: true
        };
      }
    }

    // 如果没有平台信息，使用严格的基础验证
    return this.performBasicVerification(session, duration, durationSeconds);
  }

  /**
   * 处理自动检测结果
   */
  private processAutomatedDetectionResults(
    session: TaskExecutionSession,
    detectionResults: any,
    durationSeconds: number
  ): ExecutionResult {
    const completedRequirements: TaskType[] = [];
    const failedRequirements: TaskType[] = [];

    // 检查每个要求的任务是否被检测到
    for (const requirement of session.selectedRequirements) {
      const detected = detectionResults.detectedActions.some((action: any) =>
        action.action === requirement
      );

      if (detected) {
        completedRequirements.push(requirement);
      } else {
        failedRequirements.push(requirement);
      }
    }

    const pointsEarned = this.calculatePoints(completedRequirements);
    const successRate = completedRequirements.length / session.selectedRequirements.length;

    let message = '';
    if (successRate === 1) {
      message = `🎉 任务自动验证成功！检测到所有操作完成。执行时长：${durationSeconds}秒`;
    } else if (successRate > 0) {
      message = `✅ 部分任务自动验证成功（${completedRequirements.length}/${session.selectedRequirements.length}）。已检测到：${completedRequirements.join('、')}`;
    } else {
      message = `❌ 未检测到任何操作完成。请确保您实际执行了所需操作。`;
    }

    // 清除检测结果
    automatedDetectionService.clearDetectionResults(session.id);

    return {
      success: completedRequirements.length > 0,
      completedRequirements,
      failedRequirements,
      pointsEarned,
      message,
      needsManualReview: successRate < 1, // 如果没有全部完成，需要人工审核
      proofRequired: successRate === 0 // 如果没有检测到任何操作，需要提供证明
    };
  }

  /**
   * 处理验证结果
   */
  private processVerificationResults(
    session: TaskExecutionSession,
    verificationResults: Map<TaskType, VerificationResult>,
    durationSeconds: number
  ): ExecutionResult {
    const completedRequirements: TaskType[] = [];
    const failedRequirements: TaskType[] = [];
    let totalConfidence = 0;
    let needsManualReview = false;
    let proofRequired = false;
    const messages: string[] = [];

    for (const [taskType, result] of verificationResults) {
      if (result.success && result.confidence >= 60) {
        completedRequirements.push(taskType);
      } else {
        failedRequirements.push(taskType);
      }

      totalConfidence += result.confidence;

      if (result.needsManualReview) {
        needsManualReview = true;
      }

      if (result.failureReason) {
        messages.push(`${taskType}: ${result.failureReason}`);
      }
    }

    const avgConfidence = verificationResults.size > 0 ? totalConfidence / verificationResults.size : 0;
    const pointsEarned = this.calculatePoints(completedRequirements);

    // 检查是否需要截图证明
    proofRequired = this.shouldRequireProof(session) || avgConfidence < 80;

    let message = '';
    if (completedRequirements.length === session.selectedRequirements.length) {
      message = `任务验证成功！执行时长：${durationSeconds}秒，验证置信度：${Math.round(avgConfidence)}%`;
    } else if (completedRequirements.length > 0) {
      message = `部分任务验证成功（${completedRequirements.length}/${session.selectedRequirements.length}）`;
    } else {
      message = `任务验证失败。${messages.join('; ')}`;
    }

    return {
      success: completedRequirements.length > 0,
      completedRequirements,
      failedRequirements,
      pointsEarned,
      message,
      needsManualReview,
      proofRequired
    };
  }

  /**
   * 基础验证（严格版 - 不再自动通过）
   */
  private performBasicVerification(
    session: TaskExecutionSession,
    duration: number,
    durationSeconds: number
  ): ExecutionResult {
    const isQuickCompletion = duration < this.QUICK_COMPLETION_THRESHOLD;

    // 严格验证：没有行为数据的情况下，默认失败
    // 这解决了用户不实际操作就能获得积分的漏洞

    if (isQuickCompletion) {
      return {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: `任务完成时间过短（${durationSeconds}秒），无法验证操作真实性。请重新完成任务并确保实际执行了所需操作。`,
        proofRequired: true,
        needsManualReview: true
      };
    }

    // 对于较长时间的任务，也需要基本的验证
    // 如果没有综合验证数据，要求提供证明
    return {
      success: false,
      completedRequirements: [],
      failedRequirements: session.selectedRequirements,
      pointsEarned: 0,
      message: `无法验证任务完成情况。请确保您实际执行了所需操作（如点赞、分享等），并提供完成截图作为证明。`,
      proofRequired: true,
      needsManualReview: true
    };
  }

  /**
   * 计算积分奖励
   */
  private calculatePoints(requirements: TaskType[]): number {
    const pointsMap: Record<TaskType, number> = {
      like: 10,
      share: 15,
      comment: 20,
      follow: 25
    };

    return requirements.reduce((total, req) => total + (pointsMap[req] || 0), 0);
  }

  /**
   * 判断是否需要提供证明
   */
  private shouldRequireProof(session: TaskExecutionSession): boolean {
    const totalPoints = this.calculatePoints(session.selectedRequirements);
    const duration = Date.now() - session.startTime;

    // 需要证明的情况：
    // 1. 高价值任务（50积分以上）
    // 2. 多种需求类型（超过2种）
    // 3. 快速完成（5秒内）
    // 4. 新用户或低信誉用户的任务

    return (
      totalPoints >= 50 ||
      session.selectedRequirements.length > 2 ||
      duration < this.QUICK_COMPLETION_THRESHOLD
    );
  }

  /**
   * 显示完成确认对话框
   */
  private showCompletionDialog(session: TaskExecutionSession, result: ExecutionResult): void {
    // 触发自定义事件，让UI组件处理对话框显示
    const event = new CustomEvent('taskExecutionComplete', {
      detail: { session, result }
    });
    window.dispatchEvent(event);
  }

  /**
   * 提交任务完成
   */
  async submitTaskCompletion(
    sessionId: string,
    proof?: ExecutionProof
  ): Promise<TaskExecution> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('会话不存在或已过期');
    }

    try {
      // 尝试调用真实API
      const response = await api.post('/tasks/execute', {
        task_id: session.taskId,
        selected_requirements: session.selectedRequirements,
        execution_duration: Date.now() - session.startTime,
        proof: proof ? {
          screenshots: proof.screenshots?.map(file => file.name),
          description: proof.description,
          timestamp: proof.timestamp
        } : undefined
      });

      this.activeSessions.delete(sessionId);
      return response.data;
    } catch (error: any) {
      // 如果是404错误（API不存在），创建模拟响应
      if (error.response?.status === 404) {
        console.log('API endpoint not found, using mock response');

        // 创建模拟的任务执行记录
        const mockTaskExecution: TaskExecution = {
          id: Date.now(),
          task_id: session.taskId,
          user_id: 1, // 模拟用户ID
          selected_requirements: session.selectedRequirements,
          execution_duration: Date.now() - session.startTime,
          status: 'completed' as ExecutionStatus,
          points_earned: this.calculatePoints(session.selectedRequirements),
          proof: proof ? {
            screenshots: proof.screenshots?.map(file => file.name) || [],
            description: proof.description || '',
            timestamp: proof.timestamp
          } : undefined,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        this.activeSessions.delete(sessionId);

        // 模拟延迟以提供真实感
        await new Promise(resolve => setTimeout(resolve, 500));

        return mockTaskExecution;
      }

      // 对于其他错误，抛出原始错误
      throw new Error(error.response?.data?.message || '提交任务失败');
    }
  }

  /**
   * 处理会话超时
   */
  private handleSessionTimeout(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (session && session.status === 'monitoring') {
      session.status = 'timeout';
      if (session.windowRef && !session.windowRef.closed) {
        session.windowRef.close();
      }
      
      this.showCompletionDialog(session, {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: '任务执行超时，请重新开始'
      });
      
      this.activeSessions.delete(sessionId);
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取活跃会话
   */
  getActiveSession(sessionId: string): TaskExecutionSession | undefined {
    return this.activeSessions.get(sessionId);
  }

  /**
   * 取消任务执行
   */
  cancelExecution(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      if (session.windowRef && !session.windowRef.closed) {
        session.windowRef.close();
      }
      this.activeSessions.delete(sessionId);
    }
  }
}

export const taskExecutionService = new TaskExecutionService();
