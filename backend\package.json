{"name": "mutual-like-backend", "version": "1.0.0", "description": "互助点赞平台后端API服务", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec \"ts-node --transpile-only -r tsconfig-paths/register\" src/index.ts", "build": "tsc && tsc-alias", "start": "ts-node -r tsconfig-paths/register src/index.ts", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "knex migrate:latest", "db:rollback": "knex migrate:rollback", "db:seed": "knex seed:run", "db:reset": "knex migrate:rollback --all && knex migrate:latest && knex seed:run", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["nodejs", "express", "typescript", "postgresql", "jwt"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^11.1.0", "winston": "^3.11.0"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.57.1", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.2.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}