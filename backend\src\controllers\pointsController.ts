import { Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { ApiResponse, PointTransaction, User } from '@/types';
import { FileDB } from '@/services/fileDatabase';

/**
 * 获取积分统计
 */
export const getPointsStats = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const userId = (req as any).user?.userId;

  if (!userId) {
    res.status(401).json({ success: false, message: '未登录' });
    return;
  }

  const user = await FileDB.getById<User>('users', userId);
  if (!user) {
    res.status(404).json({ success: false, message: '用户不存在' });
    return;
  }

  const now = new Date();
  const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const thisWeekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  const allTransactions = await FileDB.find<PointTransaction>('pointTransactions', t => t.user_id === userId);

  const getSum = (transactions: PointTransaction[], type: string, fromDate: Date) => {
    return transactions
      .filter(t => t.transaction_type === type && new Date(t.created_at) >= fromDate)
      .reduce((sum, t) => sum + t.amount, 0);
  };
  
  const totalEarned = allTransactions
    .filter(t => t.transaction_type === 'reward')
    .reduce((sum, t) => sum + t.amount, 0);

  const stats = {
    total_points: user.points_balance,
    earned_this_month: getSum(allTransactions, 'reward', thisMonthStart),
    spent_this_month: Math.abs(getSum(allTransactions, 'cost', thisMonthStart)),
    earned_this_week: getSum(allTransactions, 'reward', thisWeekStart),
    spent_this_week: Math.abs(getSum(allTransactions, 'cost', thisWeekStart)),
    earned_today: getSum(allTransactions, 'reward', todayStart),
    spent_today: Math.abs(getSum(allTransactions, 'cost', todayStart)),
    total_earned: totalEarned,
  };

  res.json({ success: true, message: '获取积分统计成功', data: stats });
});

/**
 * 获取积分交易记录
 */
export const getPointsTransactions = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const userId = (req as any).user?.userId;
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const type = req.query.type as string;

  if (!userId) {
    res.status(401).json({ success: false, message: '未登录' });
    return;
  }

  let transactions = await FileDB.find<PointTransaction>('pointTransactions', t => t.user_id === userId);

  if (type) {
    transactions = transactions.filter(t => t.transaction_type === type);
  }

  transactions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  const total = transactions.length;
  const offset = (page - 1) * limit;
  const paginatedTransactions = transactions.slice(offset, offset + limit);

  res.json({
    success: true,
    message: '获取积分交易记录成功',
    data: {
      transactions: paginatedTransactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
});
