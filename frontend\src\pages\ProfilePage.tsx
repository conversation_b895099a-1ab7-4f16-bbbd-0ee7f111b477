import React, { useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Edit3,
  Save,
  X,
  Camera,
  Shield,
  Eye,
  EyeOff,
  Key,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  Setting<PERSON>,
  Link,
  Unlink,
} from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { AuthService } from '@/services/authService';
import { toast } from 'react-hot-toast';

interface ProfileFormData {
  username: string;
  email: string;
  phone?: string;
  bio?: string;
  location?: string;
  birth_date?: string;
}

interface PasswordFormData {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

const ProfilePage: React.FC = () => {
  const { user, updateUser } = useAuthStore();
  const queryClient = useQueryClient();
  
  const [isEditing, setIsEditing] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const [profileForm, setProfileForm] = useState<ProfileFormData>({
    username: user?.username || '',
    email: user?.email || '',
    phone: user?.phone || '',
    bio: user?.bio || '',
    location: user?.location || '',
    birth_date: user?.birth_date || '',
  });
  
  const [passwordForm, setPasswordForm] = useState<PasswordFormData>({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });

  // 更新个人资料
  const updateProfileMutation = useMutation(
    async (data: ProfileFormData) => {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      return AuthService.updateProfile(data);
    },
    {
      onSuccess: (data) => {
        if (data.data?.user) {
          updateUser(data.data.user);
        }
        setIsEditing(false);
        toast.success('个人资料更新成功');
        queryClient.invalidateQueries(['user-profile']);
      },
      onError: (error: any) => {
        toast.error(error.message || '更新失败，请重试');
      },
    }
  );

  // 修改密码
  const changePasswordMutation = useMutation(
    async (data: PasswordFormData) => {
      if (data.new_password !== data.confirm_password) {
        throw new Error('新密码和确认密码不匹配');
      }
      if (data.new_password.length < 6) {
        throw new Error('新密码长度至少6位');
      }
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { success: true };
    },
    {
      onSuccess: () => {
        setShowPasswordForm(false);
        setPasswordForm({
          current_password: '',
          new_password: '',
          confirm_password: '',
        });
        toast.success('密码修改成功');
      },
      onError: (error: any) => {
        toast.error(error.message || '密码修改失败，请重试');
      },
    }
  );

  const handleProfileSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfileMutation.mutate(profileForm);
  };

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    changePasswordMutation.mutate(passwordForm);
  };

  const handleCancel = () => {
    setProfileForm({
      username: user?.username || '',
      email: user?.email || '',
      phone: user?.phone || '',
      bio: user?.bio || '',
      location: user?.location || '',
      birth_date: user?.birth_date || '',
    });
    setIsEditing(false);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '未设置';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getAccountAge = () => {
    if (!user?.created_at) return '未知';
    const created = new Date(user.created_at);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays < 30) {
      return `${diffInDays} 天`;
    } else if (diffInDays < 365) {
      return `${Math.floor(diffInDays / 30)} 个月`;
    } else {
      return `${Math.floor(diffInDays / 365)} 年`;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">个人资料</h1>
          <p className="text-gray-600 mt-1">管理您的个人信息和账户设置</p>
        </div>
        <div className="flex items-center space-x-2">
          {!isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Edit3 className="w-4 h-4" />
              <span>编辑资料</span>
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：个人信息卡片 */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="text-center">
              {/* 头像 */}
              <div className="relative inline-block">
                <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                  {user?.username?.charAt(0).toUpperCase() || 'U'}
                </div>
                <button className="absolute bottom-0 right-0 p-1.5 bg-white rounded-full shadow-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                  <Camera className="w-3 h-3 text-gray-600" />
                </button>
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mt-4">
                {user?.username || '未设置用户名'}
              </h3>
              <p className="text-gray-500 text-sm">
                {user?.email || '未设置邮箱'}
              </p>
              
              {/* 账户状态 */}
              <div className="flex items-center justify-center space-x-2 mt-3">
                <div className="flex items-center space-x-1 text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm">已验证</span>
                </div>
              </div>
              
              {/* 统计信息 */}
              <div className="grid grid-cols-2 gap-4 mt-6 pt-6 border-t border-gray-200">
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">
                    {user?.points_balance || 0}
                  </p>
                  <p className="text-sm text-gray-500">积分余额</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">
                    {getAccountAge()}
                  </p>
                  <p className="text-sm text-gray-500">账户年龄</p>
                </div>
              </div>
            </div>
          </div>

          {/* 社交账户绑定 */}
          <div className="card mt-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">社交账户</h4>
            <div className="space-y-3">
              {[
                { name: '微信', connected: false, color: 'text-green-600' },
                { name: '抖音', connected: !!user?.social_accounts?.douyin, color: 'text-red-600' },
                { name: '小红书', connected: !!user?.social_accounts?.xiaohongshu, color: 'text-pink-600' },
                { name: '快手', connected: !!user?.social_accounts?.kuaishou, color: 'text-orange-600' },
              ].map((platform) => (
                <div key={platform.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-900">{platform.name}</span>
                  {platform.connected ? (
                    <button className="flex items-center space-x-1 text-red-600 hover:text-red-700">
                      <Unlink className="w-4 h-4" />
                      <span className="text-sm">解绑</span>
                    </button>
                  ) : (
                    <button className="flex items-center space-x-1 text-blue-600 hover:text-blue-700">
                      <Link className="w-4 h-4" />
                      <span className="text-sm">绑定</span>
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧：详细信息表单 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 基本信息 */}
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">基本信息</h3>
              {isEditing && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleCancel}
                    className="flex items-center space-x-1 px-3 py-1.5 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    <X className="w-4 h-4" />
                    <span>取消</span>
                  </button>
                  <button
                    onClick={handleProfileSubmit}
                    disabled={updateProfileMutation.isLoading}
                    className="flex items-center space-x-1 px-3 py-1.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                  >
                    <Save className="w-4 h-4" />
                    <span>{updateProfileMutation.isLoading ? '保存中...' : '保存'}</span>
                  </button>
                </div>
              )}
            </div>

            <form onSubmit={handleProfileSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 用户名 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    用户名
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={profileForm.username}
                      onChange={(e) => setProfileForm({ ...profileForm, username: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  ) : (
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <User className="w-4 h-4 text-gray-500" />
                      <span>{user?.username || '未设置'}</span>
                    </div>
                  )}
                </div>

                {/* 邮箱 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    邮箱地址
                  </label>
                  {isEditing ? (
                    <input
                      type="email"
                      value={profileForm.email}
                      onChange={(e) => setProfileForm({ ...profileForm, email: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  ) : (
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <Mail className="w-4 h-4 text-gray-500" />
                      <span>{user?.email || '未设置'}</span>
                    </div>
                  )}
                </div>

                {/* 手机号 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    手机号码
                  </label>
                  {isEditing ? (
                    <input
                      type="tel"
                      value={profileForm.phone}
                      onChange={(e) => setProfileForm({ ...profileForm, phone: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <Phone className="w-4 h-4 text-gray-500" />
                      <span>{user?.phone || '未设置'}</span>
                    </div>
                  )}
                </div>

                {/* 生日 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    生日
                  </label>
                  {isEditing ? (
                    <input
                      type="date"
                      value={profileForm.birth_date}
                      onChange={(e) => setProfileForm({ ...profileForm, birth_date: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <Calendar className="w-4 h-4 text-gray-500" />
                      <span>{formatDate(user?.birth_date)}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* 地址 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  所在地区
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={profileForm.location}
                    onChange={(e) => setProfileForm({ ...profileForm, location: e.target.value })}
                    placeholder="请输入您的所在地区"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                    <MapPin className="w-4 h-4 text-gray-500" />
                    <span>{user?.location || '未设置'}</span>
                  </div>
                )}
              </div>

              {/* 个人简介 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  个人简介
                </label>
                {isEditing ? (
                  <textarea
                    value={profileForm.bio}
                    onChange={(e) => setProfileForm({ ...profileForm, bio: e.target.value })}
                    placeholder="介绍一下自己吧..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  />
                ) : (
                  <div className="p-3 bg-gray-50 rounded-lg min-h-[100px]">
                    <span className="text-gray-700">
                      {user?.bio || '这个人很懒，什么都没有留下...'}
                    </span>
                  </div>
                )}
              </div>
            </form>
          </div>

          {/* 安全设置 */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">安全设置</h3>
            
            <div className="space-y-4">
              {/* 修改密码 */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-full">
                    <Key className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">登录密码</p>
                    <p className="text-sm text-gray-500">定期更换密码可以提高账户安全性</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowPasswordForm(!showPasswordForm)}
                  className="px-4 py-2 text-blue-600 hover:text-blue-700 font-medium transition-colors"
                >
                  修改密码
                </button>
              </div>

              {/* 密码修改表单 */}
              {showPasswordForm && (
                <div className="p-4 border border-gray-200 rounded-lg">
                  <form onSubmit={handlePasswordSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        当前密码
                      </label>
                      <div className="relative">
                        <input
                          type={showCurrentPassword ? 'text' : 'password'}
                          value={passwordForm.current_password}
                          onChange={(e) => setPasswordForm({ ...passwordForm, current_password: e.target.value })}
                          className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                          {showCurrentPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        新密码
                      </label>
                      <div className="relative">
                        <input
                          type={showNewPassword ? 'text' : 'password'}
                          value={passwordForm.new_password}
                          onChange={(e) => setPasswordForm({ ...passwordForm, new_password: e.target.value })}
                          className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                          {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        确认新密码
                      </label>
                      <div className="relative">
                        <input
                          type={showConfirmPassword ? 'text' : 'password'}
                          value={passwordForm.confirm_password}
                          onChange={(e) => setPasswordForm({ ...passwordForm, confirm_password: e.target.value })}
                          className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                          {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <button
                        type="submit"
                        disabled={changePasswordMutation.isLoading}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                      >
                        {changePasswordMutation.isLoading ? '修改中...' : '确认修改'}
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setShowPasswordForm(false);
                          setPasswordForm({
                            current_password: '',
                            new_password: '',
                            confirm_password: '',
                          });
                        }}
                        className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                      >
                        取消
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {/* 两步验证 */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-100 rounded-full">
                    <Shield className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">两步验证</p>
                    <p className="text-sm text-gray-500">为您的账户添加额外的安全保护</p>
                  </div>
                </div>
                <button className="px-4 py-2 text-blue-600 hover:text-blue-700 font-medium transition-colors">
                  启用
                </button>
              </div>

              {/* 登录设备 */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-purple-100 rounded-full">
                    <Settings className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">登录设备管理</p>
                    <p className="text-sm text-gray-500">查看和管理您的登录设备</p>
                  </div>
                </div>
                <button className="px-4 py-2 text-blue-600 hover:text-blue-700 font-medium transition-colors">
                  管理
                </button>
              </div>
            </div>
          </div>

          {/* 危险操作 */}
          <div className="card border-red-200">
            <h3 className="text-lg font-semibold text-red-600 mb-6">危险操作</h3>
            
            <div className="p-4 bg-red-50 rounded-lg border border-red-200">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                <div className="flex-1">
                  <h4 className="font-medium text-red-900 mb-2">删除账户</h4>
                  <p className="text-sm text-red-700 mb-4">
                    删除账户将永久移除您的所有数据，包括积分记录、任务历史等。此操作不可恢复。
                  </p>
                  <button className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    <Trash2 className="w-4 h-4" />
                    <span>删除账户</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
