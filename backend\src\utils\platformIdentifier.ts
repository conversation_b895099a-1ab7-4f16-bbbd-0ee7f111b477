import { Platform } from '@/types';

// 定义每个平台的识别规则
const platformRules: { platform: Platform; hostnames: string[]; paths: string[]; patterns?: RegExp[]; specialHostnames?: string[] }[] = [
  {
    platform: 'youtube',
    hostnames: ['youtube.com', 'youtu.be'],
    paths: ['/shorts/', '/watch'],
    specialHostnames: ['youtu.be'],
  },
  {
    platform: 'tiktok',
    hostnames: ['tiktok.com', 'vm.tiktok.com'],
    paths: ['/video/'],
    specialHostnames: ['vm.tiktok.com'],
  },
  {
    platform: 'douyin',
    hostnames: ['douyin.com', 'v.douyin.com'],
    paths: ['/video/', '/note/', '?recommend='],
    specialHostnames: ['v.douyin.com'],
  },
  {
    platform: 'kuaishou',
    hostnames: ['kuaishou.com', 'v.kuaishou.com'],
    paths: ['/short-video/'],
    specialHostnames: ['v.kuaishou.com'],
  },
  {
    platform: 'bilibili',
    hostnames: ['bilibili.com', 'b23.tv'],
    paths: ['/video/'],
    patterns: [/\/video\/BV[a-zA-Z0-9]+/],
    specialHostnames: ['b23.tv'],
  },
];

/**
 * 根据URL使用规则集识别短视频平台
 * @param url a string that might be a URL
 * @returns a Platform string or 'Unknown'
 */
export const identifyPlatform = (url: string): Platform | 'Unknown' => {
  if (!url || typeof url !== 'string') {
    console.log(`[Platform Identifier] Empty or invalid URL: ${url}`);
    return 'Unknown';
  }

  const urlRegex = /(https?:\/\/[^\s"'<>`]+)/;
  const match = url.match(urlRegex);

  if (!match) {
    console.log(`[Platform Identifier] URL doesn't match regex: ${url}`);
    return 'Unknown';
  }
  const extractedUrl = match[0];
  console.log(`[Platform Identifier] Extracted URL: ${extractedUrl}`);

  try {
    const parsedUrl = new URL(extractedUrl);
    const host = parsedUrl.hostname.toLowerCase();
    const fullPath = parsedUrl.pathname + parsedUrl.search;
    console.log(`[Platform Identifier] Host: ${host}, Path: ${fullPath}`);

    for (const rule of platformRules) {
      // 检查当前URL的域名是否与规则匹配 (完全匹配或作为子域名匹配)
      const matchedHostname = rule.hostnames.find(h => host === h || host.endsWith(`.${h}`));

      if (matchedHostname) {
        console.log(`[Platform Identifier] Matched hostname for ${rule.platform}: ${matchedHostname}`);
        
        // 如果是特殊短域名, 则无需检查路径, 直接返回平台
        if (rule.specialHostnames?.includes(host)) {
          console.log(`[Platform Identifier] Special hostname detected, returning ${rule.platform}`);
          return rule.platform;
        }
        
        // 检查路径是否匹配
        if (rule.paths.some(path => fullPath.includes(path))) {
          console.log(`[Platform Identifier] Path matched for ${rule.platform}`);
          return rule.platform;
        }

        // 检查正则表达式模式是否匹配
        if (rule.patterns && rule.patterns.some(pattern => pattern.test(fullPath))) {
          console.log(`[Platform Identifier] Pattern matched for ${rule.platform}`);
          return rule.platform;
        }
      }
    }

    console.log(`[Platform Identifier] No platform matched for ${extractedUrl}`);
    return 'Unknown';
  } catch (error) {
    console.error(`[Platform Identifier] Invalid URL format: ${extractedUrl}`, error);
    return 'Unknown';
  }
};
