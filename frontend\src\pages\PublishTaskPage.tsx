import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';
import { useDebouncedCallback } from 'use-debounce';
import {
  Heart, Share, UserPlus, MessageCircle, AlertCircle, CheckCircle, Loader2, Link as LinkIcon,
  Music, Globe, Zap, Youtube, Book, Minus, Plus, Wallet, TrendingUp, TrendingDown, Info, User
} from 'lucide-react';
import { Platform, TaskType } from '@/types';
import { useAuthStore } from '@/store/authStore';
import { TaskService, TaskItem } from '@/services/taskService';
import { UtilsService } from '@/services/utilsService';
import { getVideoInfo, VideoInfo, VideoPlatform } from '@/services/videoService';

const platformInfo: Record<Platform, { icon: React.ReactNode; name: string }> = {
  douyin: { icon: <Music size={18} />, name: '抖音' },
  tiktok: { icon: <Globe size={18} />, name: 'TikTok' },
  kuaishou: { icon: <Zap size={18} />, name: '快手' },
  bilibili: { icon: <Youtube size={18} />, name: 'Bilibili' },
  xiaohongshu: { icon: <Book size={18} />, name: '小红书' },
  youtube: { icon: <Youtube size={18} />, name: 'YouTube' },
};

const taskTypesConfig: Record<TaskType, { name: string; icon: React.ElementType; points: number; color: string }> = {
  like: { name: '点赞', icon: Heart, points: 10, color: 'text-red-500' },
  share: { name: '分享', icon: Share, points: 15, color: 'text-blue-500' },
  comment: { name: '评论', icon: MessageCircle, points: 20, color: 'text-green-500' },
  follow: { name: '关注', icon: UserPlus, points: 25, color: 'text-purple-500' },
};

const PublishTaskPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, updateUserPoints } = useAuthStore();
  const queryClient = useQueryClient();

  const [videoUrl, setVideoUrl] = useState('');
  const [tasks, setTasks] = useState<Record<TaskType, number>>({ like: 0, share: 0, comment: 0, follow: 0 });
  const [identifiedPlatform, setIdentifiedPlatform] = useState<Platform | null>(null);
  const [isIdentifying, setIsIdentifying] = useState(false);
  const [identificationError, setIdentificationError] = useState<string | null>(null);
  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null);
  const [isLoadingVideoInfo, setIsLoadingVideoInfo] = useState(false);
  


  const createTasksMutation = useMutation((data: { video_url: string; tasks: TaskItem[] }) =>
    TaskService.createTasks(data.video_url, data.tasks), {
    onSuccess: () => {
      toast.success('任务发布成功！');
      queryClient.invalidateQueries('myPublishedTasks');
      // 更新用户积分
      const totalCost = calculatedCosts.totalCost;
      updateUserPoints(-totalCost);
      navigate('/my-tasks');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '发布失败，请重试');
    },
  });

  const handleUrlChange = useDebouncedCallback(async (url: string) => {
    setVideoUrl(url);
    if (!url.trim()) {
      setIdentifiedPlatform(null);
      setIdentificationError('请输入视频链接');
      setVideoInfo(null);
      return;
    }
    setIsIdentifying(true);
    setIdentificationError(null);
    setVideoInfo(null);
    
    try {
      console.log('发送请求:', url);
      const response = await UtilsService.identifyPlatform(url);
      console.log('收到响应:', response);


      if (response && response.data && response.data.platform) {
        const platform = response.data.platform as Platform;
        setIdentifiedPlatform(platform);
        // 获取视频信息
        await fetchVideoInfo(url);
      } else if (response && response.platform) {
        // 处理直接返回platform的情况
        const platform = response.platform as Platform;
        setIdentifiedPlatform(platform);
        // 获取视频信息
        await fetchVideoInfo(url);
      } else {
        setIdentifiedPlatform(null);
        setIdentificationError('无法识别链接，请检查格式或网络');
      }
    } catch (error: any) {
      console.error('识别错误:', error);
      setIdentifiedPlatform(null);
      setIdentificationError('无法识别链接，请检查格式');
    } finally {
      setIsIdentifying(false);
    }
  }, 500);

  const fetchVideoInfo = async (url: string) => {
    try {
      setIsLoadingVideoInfo(true);
      const info = await getVideoInfo(url);
      setVideoInfo(info);
    } catch (error) {
      console.error('获取视频信息失败:', error);
      // 即使获取视频信息失败，也不影响平台识别结果
    } finally {
      setIsLoadingVideoInfo(false);
    }
  };

  const handleTaskCountChange = (type: TaskType, delta: number) => {
    setTasks(prev => ({ ...prev, [type]: Math.max(0, prev[type] + delta) }));
  };

  const calculatedCosts = useMemo(() => {
    const costDetails = (Object.keys(tasks) as TaskType[]).map(type => ({
      type,
      count: tasks[type],
      cost: tasks[type] * taskTypesConfig[type].points,
    })).filter(item => item.count > 0);
    
    const totalTasks = costDetails.reduce((sum, item) => sum + item.count, 0);
    const totalCost = costDetails.reduce((sum, item) => sum + item.cost, 0);

    return { costDetails, totalTasks, totalCost };
  }, [tasks]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const tasksToSubmit: TaskItem[] = calculatedCosts.costDetails.map(({ type, count }) => ({ type, count }));
    if (!videoUrl || !identifiedPlatform) {
      toast.error('请先输入有效的视频链接');
      return;
    }
    if (tasksToSubmit.length === 0) {
      toast.error('请至少选择一个任务类型并设置数量');
      return;
    }
    if (user && user.points_balance < calculatedCosts.totalCost) {
      toast.error('积分不足！');
      return;
    }
    createTasksMutation.mutate({ video_url: videoUrl, tasks: tasksToSubmit });
  };
  
  const canSubmit = identifiedPlatform && !isIdentifying && calculatedCosts.totalTasks > 0 && !createTasksMutation.isLoading;



  return (
    <div className="flex space-x-8 p-8 bg-gray-50 min-h-screen">
      {/* Main Content */}
      <div className="flex-grow space-y-6">
        <header>
          <h1 className="text-3xl font-bold text-gray-800">智能发布任务</h1>
          <p className="text-gray-500 mt-1">只需输入视频链接，系统将自动识别平台并获取视频信息</p>
        </header>

        {/* Video Link Section */}
        <div className="bg-white p-6 rounded-lg shadow">
          <label className="text-lg font-semibold text-gray-700 flex items-center mb-3">
            <LinkIcon size={20} className="mr-2" /> 视频链接 *
          </label>
          <div className="relative">
            <input
              type="text"
              onChange={(e) => handleUrlChange(e.target.value)}
              className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500"
              placeholder="https://v.douyin.com/iYcRLb2k/"
            />
            {isIdentifying && <Loader2 className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5 animate-spin" />}
          </div>
          <div className="mt-3">
            {identifiedPlatform && platformInfo[identifiedPlatform] && (
              <div className="inline-flex items-center bg-green-50 border border-green-200 rounded-lg px-4 py-2 text-green-700">
                <CheckCircle size={16} className="mr-2 text-green-500" />
                <span className="text-sm font-medium mr-2">平台识别成功</span>
                <div className="flex items-center bg-white rounded-md px-2 py-1 border border-green-200">
                  <span className="text-lg mr-1">{platformInfo[identifiedPlatform].icon}</span>
                  <span className="font-semibold text-gray-800">{platformInfo[identifiedPlatform].name}</span>
                </div>
              </div>
            )}
            {identificationError && (
              <div className="inline-flex items-center bg-red-50 border border-red-200 rounded-lg px-4 py-2 text-red-700">
                <AlertCircle size={16} className="mr-2 text-red-500" />
                <span className="text-sm font-medium">{identificationError}</span>
              </div>
            )}
          </div>

          {/* Video Information Display */}
          {identifiedPlatform && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
              <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <Info size={16} className="mr-2" />
                视频信息
              </h3>
              {isLoadingVideoInfo ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin text-gray-400 mr-2" />
                  <span className="text-gray-500">正在获取视频信息...</span>
                </div>
              ) : videoInfo ? (
                <div className="flex space-x-4">
                  <div className="flex-shrink-0 relative">
                    <img
                      src={videoInfo.cover}
                      alt={videoInfo.title}
                      className="w-24 h-32 object-cover rounded-lg border transition-opacity duration-300"
                      onLoad={(e) => {
                        (e.target as HTMLImageElement).style.opacity = '1';
                      }}
                      onError={(e) => {
                        const img = e.target as HTMLImageElement;
                        if (!img.src.includes('placeholder-cover.svg')) {
                          console.warn('封面图片加载失败，使用占位符:', videoInfo.cover);
                          img.src = '/placeholder-cover.svg';
                          img.alt = '封面加载失败';
                        }
                      }}
                      style={{ opacity: 0 }}
                    />
                    {/* 加载指示器 */}
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg border">
                      <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                    </div>
                  </div>
                  <div className="flex-grow min-w-0">
                    <h4 className="font-semibold text-gray-800 text-sm mb-2 overflow-hidden" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      {videoInfo.title}
                    </h4>
                    <div className="space-y-1 text-xs text-gray-600">
                      <p className="flex items-center">
                        <User size={12} className="mr-1" />
                        作者: {videoInfo.author}
                      </p>
                      {videoInfo.duration && (
                        <p className="flex items-center">
                          <span className="mr-1">⏱️</span>
                          时长: {Math.floor(videoInfo.duration / 60)}:{(videoInfo.duration % 60).toString().padStart(2, '0')}
                        </p>
                      )}
                      <p className="flex items-center">
                        <span className="mr-1">{platformInfo[identifiedPlatform]?.icon}</span>
                        平台: {platformInfo[identifiedPlatform]?.name}
                      </p>
                    </div>
                    {videoInfo.description && (
                      <p className="text-xs text-gray-500 mt-2 overflow-hidden" style={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical'
                      }}>
                        {videoInfo.description}
                      </p>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500 text-sm">
                  <AlertCircle size={16} className="mx-auto mb-2" />
                  无法获取视频详细信息，但不影响任务发布
                </div>
              )}
            </div>
          )}

        </div>

        {/* Task Type & Quantity Section */}
        <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold text-gray-700 mb-4">任务类型与数量 *</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(Object.keys(taskTypesConfig) as TaskType[]).map(type => {
                    const config = taskTypesConfig[type];
                    const Icon = config.icon;
                    return (
                        <div key={type} className="border border-gray-200 rounded-lg p-4 flex items-center justify-between">
                            <div className="flex items-center">
                                <Icon size={24} className={`${config.color} mr-4`} />
                                <div>
                                    <p className="font-semibold text-gray-800">{config.name}</p>
                                    <p className="text-sm text-yellow-600 flex items-center">
                                        <Wallet size={14} className="mr-1" /> {config.points} 积分
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <button type="button" onClick={() => handleTaskCountChange(type, -1)} className="p-1 rounded-full bg-gray-200 text-gray-600 hover:bg-gray-300 disabled:opacity-50" disabled={tasks[type] <= 0}><Minus size={16} /></button>
                                <span className="font-bold text-lg w-8 text-center">{tasks[type]}</span>
                                <button type="button" onClick={() => handleTaskCountChange(type, 1)} className="p-1 rounded-full bg-indigo-500 text-white hover:bg-indigo-600"><Plus size={16} /></button>
                            </div>
                        </div>
                    );
                })}
            </div>
             <p className="text-xs text-gray-500 mt-4 flex items-center"><Info size={14} className="mr-1"/>可以同时选择多种任务类型, 每种类型将作为独立任务发布。</p>
        </div>
      </div>

      {/* Sidebar */}
      <aside className="w-80 flex-shrink-0 space-y-6">
        <div className="bg-white p-6 rounded-lg shadow sticky top-8">
          <h3 className="text-xl font-semibold text-gray-800 border-b pb-3 mb-4">费用预览</h3>
          <div className="space-y-3 text-sm">
            {calculatedCosts.costDetails.map(item => (
                <div key={item.type} className="flex justify-between items-center">
                    <span className="text-gray-600">{taskTypesConfig[item.type].name} ({item.count}个)</span>
                    <span className="font-medium text-gray-800">{item.cost} 积分</span>
                </div>
            ))}
            {calculatedCosts.costDetails.length === 0 && (
                <p className="text-gray-500 text-center py-4">请选择任务类型和数量</p>
            )}
          </div>
          <div className="border-t mt-4 pt-4 space-y-2">
            <div className="flex justify-between font-semibold">
              <span>总任务数:</span>
              <span>{calculatedCosts.totalTasks} 个</span>
            </div>
            <div className="flex justify-between font-bold text-lg text-indigo-600">
              <span>总费用:</span>
              <span>{calculatedCosts.totalCost} 积分</span>
            </div>
            <div className="flex justify-between text-xs text-gray-500 pt-2">
              <span className="flex items-center"><Wallet size={14} className="mr-1" />当前积分</span>
              <span>{user?.points_balance || 0}</span>
            </div>
          </div>
          
          <div className="mt-6">
            <button
              onClick={handleSubmit}
              disabled={!canSubmit}
              className="w-full py-3 px-4 bg-indigo-600 text-white font-semibold rounded-lg shadow-md hover:bg-indigo-700 transition-all disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {createTasksMutation.isLoading ? '发布中...' : `确认发布 ${calculatedCosts.totalTasks} 个任务`}
            </button>
            <button
                onClick={() => navigate('/dashboard')}
                className="w-full mt-2 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-md"
            >
                取消
            </button>
            {!canSubmit && calculatedCosts.totalTasks > 0 && (
                 <p className="text-yellow-600 text-xs text-center mt-2 flex items-center justify-center"><AlertCircle size={14} className="mr-1"/>请先输入有效的视频链接</p>
            )}
          </div>
        </div>
      </aside>
    </div>
  );
};

export default PublishTaskPage;
