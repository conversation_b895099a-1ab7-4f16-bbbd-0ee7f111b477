import { TaskType } from '@/types';
import api from './api';

// 验证结果类型
export interface VerificationResult {
  success: boolean;
  confidence: number; // 0-100 置信度
  method: VerificationMethod;
  evidence?: VerificationEvidence;
  needsManualReview?: boolean;
  failureReason?: string;
  timestamp: number;
}

// 验证方法类型
export type VerificationMethod = 
  | 'api_verification'
  | 'dom_state_detection'
  | 'screenshot_analysis'
  | 'behavior_analysis'
  | 'time_based'
  | 'hybrid';

// 验证证据类型
export interface VerificationEvidence {
  screenshots?: File[];
  domChanges?: DOMChangeRecord[];
  behaviorMetrics?: BehaviorMetrics;
  apiResponse?: any;
  userInteractions?: UserInteraction[];
}

// DOM变化记录
export interface DOMChangeRecord {
  element: string;
  property: string;
  beforeValue: string;
  afterValue: string;
  timestamp: number;
}

// 行为指标
export interface BehaviorMetrics {
  totalTime: number;
  activeTime: number;
  clickCount: number;
  scrollDistance: number;
  mouseMovements: number;
  keystrokes: number;
  focusEvents: number;
}

// 用户交互记录
export interface UserInteraction {
  type: 'click' | 'scroll' | 'keypress' | 'focus' | 'blur';
  target: string;
  timestamp: number;
  coordinates?: { x: number; y: number };
  value?: string;
}

// 平台特定验证配置
export interface PlatformVerificationConfig {
  platform: string;
  supportedMethods: VerificationMethod[];
  apiEndpoints?: {
    checkLike?: string;
    checkShare?: string;
    checkComment?: string;
    checkFollow?: string;
  };
  domSelectors?: {
    likeButton?: string;
    shareButton?: string;
    commentBox?: string;
    followButton?: string;
  };
  behaviorThresholds?: {
    minActiveTime?: number;
    minClicks?: number;
    maxCompletionTime?: number;
  };
}

// 任务验证服务
export class TaskVerificationService {
  private platformConfigs: Map<string, PlatformVerificationConfig> = new Map();
  private verificationHistory: Map<string, VerificationResult[]> = new Map();

  constructor() {
    this.initializePlatformConfigs();
  }

  /**
   * 初始化平台验证配置
   */
  private initializePlatformConfigs(): void {
    // B站配置
    this.platformConfigs.set('bilibili', {
      platform: 'bilibili',
      supportedMethods: ['dom_state_detection', 'behavior_analysis', 'screenshot_analysis'],
      domSelectors: {
        likeButton: '.video-like, .like-btn, [data-v-*] .like',
        shareButton: '.video-share, .share-btn',
        commentBox: '.reply-box-textarea, .comment-input',
        followButton: '.follow-btn, .subscribe-btn'
      },
      behaviorThresholds: {
        minActiveTime: 2000, // 2秒最小活跃时间
        minClicks: 1,
        maxCompletionTime: 60000 // 1分钟最大完成时间
      }
    });

    // 抖音配置
    this.platformConfigs.set('douyin', {
      platform: 'douyin',
      supportedMethods: ['dom_state_detection', 'behavior_analysis', 'screenshot_analysis'],
      domSelectors: {
        likeButton: '.like-btn, .digg-btn',
        shareButton: '.share-btn',
        commentBox: '.comment-input',
        followButton: '.follow-btn'
      },
      behaviorThresholds: {
        minActiveTime: 2000,
        minClicks: 1,
        maxCompletionTime: 60000
      }
    });

    // 快手配置
    this.platformConfigs.set('kuaishou', {
      platform: 'kuaishou',
      supportedMethods: ['dom_state_detection', 'behavior_analysis', 'screenshot_analysis'],
      domSelectors: {
        likeButton: '.like-btn, .praise-btn',
        shareButton: '.share-btn',
        commentBox: '.comment-input',
        followButton: '.follow-btn'
      },
      behaviorThresholds: {
        minActiveTime: 2000,
        minClicks: 1,
        maxCompletionTime: 60000
      }
    });

    // 小红书配置
    this.platformConfigs.set('xiaohongshu', {
      platform: 'xiaohongshu',
      supportedMethods: ['dom_state_detection', 'behavior_analysis', 'screenshot_analysis'],
      domSelectors: {
        likeButton: '.like-btn, .heart-btn',
        shareButton: '.share-btn',
        commentBox: '.comment-input',
        followButton: '.follow-btn'
      },
      behaviorThresholds: {
        minActiveTime: 2000,
        minClicks: 1,
        maxCompletionTime: 60000
      }
    });
  }

  /**
   * 验证任务完成情况
   */
  async verifyTaskCompletion(
    platform: string,
    taskTypes: TaskType[],
    evidence: VerificationEvidence,
    sessionId: string
  ): Promise<Map<TaskType, VerificationResult>> {
    const config = this.platformConfigs.get(platform);
    if (!config) {
      throw new Error(`不支持的平台: ${platform}`);
    }

    const results = new Map<TaskType, VerificationResult>();

    for (const taskType of taskTypes) {
      try {
        const result = await this.verifySpecificTask(config, taskType, evidence, sessionId);
        results.set(taskType, result);
      } catch (error) {
        console.error(`验证任务 ${taskType} 失败:`, error);
        results.set(taskType, {
          success: false,
          confidence: 0,
          method: 'hybrid',
          failureReason: `验证过程出错: ${error.message}`,
          timestamp: Date.now(),
          needsManualReview: true
        });
      }
    }

    // 保存验证历史
    this.verificationHistory.set(sessionId, Array.from(results.values()));

    return results;
  }

  /**
   * 验证特定任务类型
   */
  private async verifySpecificTask(
    config: PlatformVerificationConfig,
    taskType: TaskType,
    evidence: VerificationEvidence,
    sessionId: string
  ): Promise<VerificationResult> {
    const verificationMethods: VerificationResult[] = [];

    // 1. 行为分析验证
    if (evidence.behaviorMetrics && config.supportedMethods.includes('behavior_analysis')) {
      const behaviorResult = this.verifyBehaviorPattern(config, taskType, evidence.behaviorMetrics);
      verificationMethods.push(behaviorResult);
    }

    // 2. DOM状态检测验证
    if (evidence.domChanges && config.supportedMethods.includes('dom_state_detection')) {
      const domResult = this.verifyDOMChanges(config, taskType, evidence.domChanges);
      verificationMethods.push(domResult);
    }

    // 3. 截图分析验证
    if (evidence.screenshots && config.supportedMethods.includes('screenshot_analysis')) {
      const screenshotResult = await this.verifyScreenshots(config, taskType, evidence.screenshots);
      verificationMethods.push(screenshotResult);
    }

    // 4. 用户交互验证
    if (evidence.userInteractions) {
      const interactionResult = this.verifyUserInteractions(config, taskType, evidence.userInteractions);
      verificationMethods.push(interactionResult);
    }

    // 综合评估所有验证结果
    return this.combineVerificationResults(verificationMethods, taskType);
  }

  /**
   * 验证行为模式（增强版）
   */
  private verifyBehaviorPattern(
    config: PlatformVerificationConfig,
    taskType: TaskType,
    metrics: BehaviorMetrics
  ): VerificationResult {
    const thresholds = config.behaviorThresholds || {};
    let confidence = 0;
    const issues: string[] = [];

    // 基础时间验证
    if (thresholds.minActiveTime && metrics.activeTime < thresholds.minActiveTime) {
      issues.push(`活跃时间过短 (${metrics.activeTime}ms < ${thresholds.minActiveTime}ms)`);
    } else {
      confidence += 20;
    }

    // 严格的点击验证 - 必须有真实点击
    if (metrics.clickCount === 0) {
      issues.push('未检测到任何点击操作');
      confidence = 0; // 没有点击直接失败
    } else if (thresholds.minClicks && metrics.clickCount < thresholds.minClicks) {
      issues.push(`点击次数不足 (${metrics.clickCount} < ${thresholds.minClicks})`);
    } else {
      confidence += 40; // 点击是最重要的指标
    }

    // 检查总时间合理性
    if (thresholds.maxCompletionTime && metrics.totalTime > thresholds.maxCompletionTime) {
      issues.push(`完成时间过长 (${metrics.totalTime}ms > ${thresholds.maxCompletionTime}ms)`);
    } else if (metrics.totalTime < 2000) {
      issues.push('完成时间过短，可能是脚本操作');
      confidence -= 30;
    } else {
      confidence += 15;
    }

    // 鼠标移动验证 - 真实用户必须有鼠标移动
    if (metrics.mouseMovements === 0) {
      issues.push('未检测到鼠标移动，可能是脚本操作');
      confidence -= 20;
    } else if (metrics.mouseMovements < 5) {
      issues.push('鼠标移动过少，行为可疑');
      confidence -= 10;
    } else {
      confidence += 15;
    }

    // 特定任务类型验证
    if (taskType === 'comment' && metrics.keystrokes === 0) {
      issues.push('评论任务未检测到键盘输入');
      confidence -= 30;
    } else if (taskType === 'comment' && metrics.keystrokes > 0) {
      confidence += 20;
    }

    // 行为一致性检查
    const activeRatio = metrics.totalTime > 0 ? metrics.activeTime / metrics.totalTime : 0;
    if (activeRatio < 0.3) {
      issues.push('活跃时间比例过低，可能未真实操作');
      confidence -= 15;
    }

    // 最终验证：必须有基本的用户交互
    const hasBasicInteraction = metrics.clickCount > 0 && metrics.mouseMovements > 0;
    if (!hasBasicInteraction) {
      confidence = 0;
      issues.push('缺少基本用户交互，验证失败');
    }

    return {
      success: confidence >= 70 && issues.length === 0 && hasBasicInteraction,
      confidence: Math.max(0, Math.min(confidence, 100)),
      method: 'behavior_analysis',
      failureReason: issues.length > 0 ? issues.join('; ') : undefined,
      timestamp: Date.now(),
      needsManualReview: confidence >= 40 && confidence < 70
    };
  }

  /**
   * 验证DOM变化（增强版 - 严格检测实际操作）
   */
  private verifyDOMChanges(
    config: PlatformVerificationConfig,
    taskType: TaskType,
    changes: DOMChangeRecord[]
  ): VerificationResult {
    const selectors = config.domSelectors || {};
    let confidence = 0;
    let relevantChanges = 0;
    let criticalChanges = 0;
    const detectedChanges: string[] = [];

    for (const change of changes) {
      // 根据任务类型检查相关的DOM变化
      switch (taskType) {
        case 'like':
          // 检查点赞按钮的关键状态变化
          if (this.isLikeButtonChange(change)) {
            relevantChanges++;
            if (this.isCriticalLikeChange(change)) {
              criticalChanges++;
              confidence += 60;
              detectedChanges.push('点赞按钮状态变化');
            } else {
              confidence += 20;
              detectedChanges.push('点赞相关DOM变化');
            }
          }
          break;
        case 'share':
          if (this.isShareButtonChange(change)) {
            relevantChanges++;
            if (this.isCriticalShareChange(change)) {
              criticalChanges++;
              confidence += 60;
              detectedChanges.push('分享操作确认');
            } else {
              confidence += 20;
              detectedChanges.push('分享相关DOM变化');
            }
          }
          break;
        case 'comment':
          if (this.isCommentChange(change)) {
            relevantChanges++;
            if (this.isCriticalCommentChange(change)) {
              criticalChanges++;
              confidence += 60;
              detectedChanges.push('评论提交确认');
            } else {
              confidence += 20;
              detectedChanges.push('评论相关DOM变化');
            }
          }
          break;
        case 'follow':
          if (this.isFollowButtonChange(change)) {
            relevantChanges++;
            if (this.isCriticalFollowChange(change)) {
              criticalChanges++;
              confidence += 60;
              detectedChanges.push('关注状态变化');
            } else {
              confidence += 20;
              detectedChanges.push('关注相关DOM变化');
            }
          }
          break;
      }
    }

    // 严格验证：必须有关键状态变化才算成功
    const hasRequiredChanges = criticalChanges > 0;
    const finalConfidence = hasRequiredChanges ? confidence : Math.min(confidence, 30);

    return {
      success: hasRequiredChanges && finalConfidence >= 60,
      confidence: Math.min(finalConfidence, 100),
      method: 'dom_state_detection',
      evidence: { domChanges: changes },
      failureReason: !hasRequiredChanges
        ? `未检测到${taskType}的关键状态变化，可能未真实完成操作`
        : relevantChanges === 0
        ? `未检测到${taskType}相关的DOM变化`
        : undefined,
      timestamp: Date.now(),
      needsManualReview: relevantChanges > 0 && !hasRequiredChanges
    };
  }

  /**
   * 检查是否为点赞按钮变化
   */
  private isLikeButtonChange(change: DOMChangeRecord): boolean {
    const element = change.element.toLowerCase();
    const property = change.property.toLowerCase();

    return (
      element.includes('like') ||
      element.includes('digg') ||
      element.includes('praise') ||
      element.includes('heart') ||
      (property === 'class' && (change.afterValue.includes('liked') || change.afterValue.includes('active'))) ||
      (property === 'aria-pressed' && change.afterValue === 'true')
    );
  }

  /**
   * 检查是否为关键的点赞状态变化
   */
  private isCriticalLikeChange(change: DOMChangeRecord): boolean {
    const property = change.property.toLowerCase();
    const beforeValue = change.beforeValue.toLowerCase();
    const afterValue = change.afterValue.toLowerCase();

    // 检查关键状态变化
    return (
      (property === 'class' && !beforeValue.includes('liked') && afterValue.includes('liked')) ||
      (property === 'class' && !beforeValue.includes('active') && afterValue.includes('active')) ||
      (property === 'aria-pressed' && beforeValue === 'false' && afterValue === 'true') ||
      (property === 'data-liked' && beforeValue === 'false' && afterValue === 'true')
    );
  }

  /**
   * 检查是否为分享按钮变化
   */
  private isShareButtonChange(change: DOMChangeRecord): boolean {
    const element = change.element.toLowerCase();
    return element.includes('share') || element.includes('forward') || element.includes('repost');
  }

  /**
   * 检查是否为关键的分享状态变化
   */
  private isCriticalShareChange(change: DOMChangeRecord): boolean {
    const property = change.property.toLowerCase();
    const afterValue = change.afterValue.toLowerCase();

    return (
      (property === 'class' && afterValue.includes('shared')) ||
      (property === 'style' && afterValue.includes('display: block')) || // 分享弹窗显示
      change.element.includes('share-dialog') || change.element.includes('share-modal')
    );
  }

  /**
   * 检查是否为评论变化
   */
  private isCommentChange(change: DOMChangeRecord): boolean {
    const element = change.element.toLowerCase();
    return element.includes('comment') || element.includes('reply') || element.includes('message');
  }

  /**
   * 检查是否为关键的评论状态变化
   */
  private isCriticalCommentChange(change: DOMChangeRecord): boolean {
    const property = change.property.toLowerCase();
    const afterValue = change.afterValue.toLowerCase();

    return (
      (property === 'childlist' && change.element.includes('comment-list')) ||
      (property === 'value' && afterValue.length > 0) || // 评论内容输入
      change.element.includes('comment-success') || change.element.includes('comment-submitted')
    );
  }

  /**
   * 检查是否为关注按钮变化
   */
  private isFollowButtonChange(change: DOMChangeRecord): boolean {
    const element = change.element.toLowerCase();
    return element.includes('follow') || element.includes('subscribe') || element.includes('attention');
  }

  /**
   * 检查是否为关键的关注状态变化
   */
  private isCriticalFollowChange(change: DOMChangeRecord): boolean {
    const property = change.property.toLowerCase();
    const beforeValue = change.beforeValue.toLowerCase();
    const afterValue = change.afterValue.toLowerCase();

    return (
      (property === 'class' && !beforeValue.includes('followed') && afterValue.includes('followed')) ||
      (property === 'textcontent' && beforeValue.includes('关注') && afterValue.includes('已关注')) ||
      (property === 'textcontent' && beforeValue.includes('follow') && afterValue.includes('following'))
    );
  }

  /**
   * 验证截图
   */
  private async verifyScreenshots(
    config: PlatformVerificationConfig,
    taskType: TaskType,
    screenshots: File[]
  ): Promise<VerificationResult> {
    try {
      // 这里应该调用图像识别API来分析截图
      // 目前先实现基础的文件验证
      if (screenshots.length === 0) {
        return {
          success: false,
          confidence: 0,
          method: 'screenshot_analysis',
          failureReason: '未提供截图证明',
          timestamp: Date.now()
        };
      }

      // 基础验证：检查文件类型和大小
      const validScreenshots = screenshots.filter(file => 
        file.type.startsWith('image/') && file.size > 1024 && file.size < 10 * 1024 * 1024
      );

      if (validScreenshots.length === 0) {
        return {
          success: false,
          confidence: 0,
          method: 'screenshot_analysis',
          failureReason: '截图文件格式或大小不符合要求',
          timestamp: Date.now()
        };
      }

      // TODO: 实现真正的图像识别验证
      // 目前给予基础分数，需要人工审核
      return {
        success: true,
        confidence: 50, // 中等置信度，需要人工审核
        method: 'screenshot_analysis',
        evidence: { screenshots },
        needsManualReview: true,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        success: false,
        confidence: 0,
        method: 'screenshot_analysis',
        failureReason: `截图验证失败: ${error.message}`,
        timestamp: Date.now()
      };
    }
  }

  /**
   * 验证用户交互
   */
  private verifyUserInteractions(
    config: PlatformVerificationConfig,
    taskType: TaskType,
    interactions: UserInteraction[]
  ): VerificationResult {
    let confidence = 0;
    let relevantInteractions = 0;

    // 分析交互模式
    const clickInteractions = interactions.filter(i => i.type === 'click');
    const scrollInteractions = interactions.filter(i => i.type === 'scroll');
    const keypressInteractions = interactions.filter(i => i.type === 'keypress');

    // 检查是否有相关的点击操作
    if (clickInteractions.length > 0) {
      relevantInteractions++;
      confidence += 30;
    }

    // 检查滚动行为（表明用户在浏览内容）
    if (scrollInteractions.length > 0) {
      confidence += 20;
    }

    // 对于评论任务，检查键盘输入
    if (taskType === 'comment' && keypressInteractions.length > 0) {
      relevantInteractions++;
      confidence += 30;
    }

    // 检查交互的时间分布（防止脚本化行为）
    const timeSpans = interactions.map((interaction, index) => 
      index > 0 ? interaction.timestamp - interactions[index - 1].timestamp : 0
    ).filter(span => span > 0);

    const avgTimeSpan = timeSpans.length > 0 ? timeSpans.reduce((a, b) => a + b, 0) / timeSpans.length : 0;
    
    // 如果交互间隔太规律，可能是脚本
    if (avgTimeSpan > 100 && avgTimeSpan < 5000) {
      confidence += 20;
    } else if (avgTimeSpan < 50) {
      confidence -= 30; // 可能是脚本行为
    }

    return {
      success: relevantInteractions > 0 && confidence >= 50,
      confidence: Math.max(0, Math.min(confidence, 100)),
      method: 'behavior_analysis',
      evidence: { userInteractions: interactions },
      failureReason: relevantInteractions === 0 ? '未检测到相关的用户交互' : undefined,
      timestamp: Date.now(),
      needsManualReview: confidence < 50 && relevantInteractions > 0
    };
  }

  /**
   * 综合多种验证结果
   */
  private combineVerificationResults(
    results: VerificationResult[],
    taskType: TaskType
  ): VerificationResult {
    if (results.length === 0) {
      return {
        success: false,
        confidence: 0,
        method: 'hybrid',
        failureReason: '无可用的验证方法',
        timestamp: Date.now(),
        needsManualReview: true
      };
    }

    // 计算加权平均置信度
    const totalConfidence = results.reduce((sum, result) => sum + result.confidence, 0);
    const avgConfidence = totalConfidence / results.length;

    // 检查是否有任何方法明确成功
    const hasSuccessfulMethod = results.some(result => result.success && result.confidence >= 70);
    
    // 检查是否需要人工审核
    const needsManualReview = results.some(result => result.needsManualReview) || avgConfidence < 60;

    // 收集失败原因
    const failureReasons = results
      .filter(result => !result.success && result.failureReason)
      .map(result => result.failureReason)
      .join('; ');

    return {
      success: hasSuccessfulMethod || avgConfidence >= 70,
      confidence: Math.round(avgConfidence),
      method: 'hybrid',
      evidence: {
        // 合并所有证据
        screenshots: results.flatMap(r => r.evidence?.screenshots || []),
        domChanges: results.flatMap(r => r.evidence?.domChanges || []),
        userInteractions: results.flatMap(r => r.evidence?.userInteractions || [])
      },
      needsManualReview,
      failureReason: !hasSuccessfulMethod && avgConfidence < 70 ? failureReasons : undefined,
      timestamp: Date.now()
    };
  }

  /**
   * 获取验证历史
   */
  getVerificationHistory(sessionId: string): VerificationResult[] | undefined {
    return this.verificationHistory.get(sessionId);
  }

  /**
   * 提交人工审核请求
   */
  async submitManualReview(
    sessionId: string,
    taskType: TaskType,
    evidence: VerificationEvidence,
    userNote?: string
  ): Promise<void> {
    try {
      await api.post('/verification/manual-review', {
        session_id: sessionId,
        task_type: taskType,
        evidence,
        user_note: userNote,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('提交人工审核失败:', error);
      throw new Error('提交人工审核失败，请稍后重试');
    }
  }
}

export const taskVerificationService = new TaskVerificationService();
