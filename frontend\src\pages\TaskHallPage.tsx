import React, { useState } from 'react';
import { useMutation } from 'react-query';
import {
  Heart,
  Share,
  UserPlus,
  MessageCircle,
  Clock,
  Coins,
  RefreshCw,
  Zap,
  Clapperboard,
  BookOpen,
  Tv,
  Loader2,
} from 'lucide-react';
import { Platform, Task, TaskType } from '@/types';
import LoadingSpinner from '@/components/LoadingSpinner';
import { TaskService } from '@/services/taskService';
import { toast } from 'react-hot-toast';

const platformOptions: {
  value: Platform;
  label: string;
  icon: React.ElementType;
  color: string;
  hoverColor: string;
}[] = [
  { value: 'douyin', label: '抖音', icon: Clapperboard, color: 'text-cyan-500', hoverColor: 'hover:bg-cyan-50' },
  { value: 'kuaishou', label: '快手', icon: Zap, color: 'text-orange-500', hoverColor: 'hover:bg-orange-50' },
  { value: 'xiaohongshu', label: '小红书', icon: BookOpen, color: 'text-red-500', hoverColor: 'hover:bg-red-50' },
  { value: 'bilibili', label: 'B站', icon: Tv, color: 'text-blue-500', hoverColor: 'hover:bg-blue-50' },
  ];

  const taskTypeOptions = [
    { value: 'like', label: '点赞', icon: Heart },
    { value: 'share', label: '分享', icon: Share },
    { value: 'follow', label: '关注', icon: UserPlus },
    { value: 'comment', label: '评论', icon: MessageCircle },
  ];

  const getTaskTypeIcon = (type: TaskType) => {
    const option = taskTypeOptions.find(opt => opt.value === type);
    return option ? option.icon : Heart;
  };

  const getPlatformInfo = (platform: Platform) => {
    return platformOptions.find(opt => opt.value === platform);
  };

  const formatTimeRemaining = (expiresAt: string) => {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diff = expiry.getTime() - now.getTime();
    if (diff <= 0) return '已过期';
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) return `${days}天${hours}小时`;
    if (hours > 0) return `${hours}小时`;
    return '即将过期';
  };


const TaskHallPage: React.FC = () => {
  const [selectedPlatform, setSelectedPlatform] = useState<Platform | null>(null);
  const [currentTask, setCurrentTask] = useState<Task | null>(null);

  const { mutate: fetchRandomTask, isLoading, error } = useMutation(
    (platform: Platform) => TaskService.getRandomTask(platform),
    {
      onSuccess: (data) => {
        setCurrentTask(data);
      },
      onError: (error: any) => {
        console.error('获取随机任务失败:', error);
        setCurrentTask(null); // Clear task on error
      },
    }
  );

  const { mutate: completeTaskMutation, isLoading: isCompleting } = useMutation(
    (taskId: number) => TaskService.completeTask(taskId),
    {
      onSuccess: (data) => {
        toast.success(`任务完成！获得 ${data.task.reward_points} 积分`);
        // 可以在这里更新用户积分显示，或者刷新下一个任务
        handleNextTask();
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.message || '任务完成失败');
      }
    }
  );

  const handlePlatformSelect = (platform: Platform) => {
    setSelectedPlatform(platform);
    fetchRandomTask(platform);
  };

  const handleNextTask = () => {
    if (selectedPlatform) {
      fetchRandomTask(selectedPlatform);
    }
  };

  const handleExecuteTask = (task: Task) => {
    // 1. 打开任务链接
    if (!task.video_url) {
        toast.error("任务链接不存在");
        return;
    }
    window.open(task.video_url, '_blank');

    // 2. 调用完成任务API
    completeTaskMutation(task.id);
  };

  const renderPlatformSelector = () => (
    <div className="text-center">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">请选择一个平台开始任务</h2>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
        {platformOptions.map(p => {
          const Icon = p.icon;
    return (
            <button
              key={p.value}
              onClick={() => handlePlatformSelect(p.value)}
              className={`flex flex-col items-center justify-center p-6 border-2 border-gray-200 rounded-xl shadow-sm transition-all duration-200 ease-in-out transform hover:-translate-y-1 hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-opacity-50 ${p.hoverColor} ${p.color} focus:ring-current`}
            >
              <Icon className="w-12 h-12 mb-3" />
              <span className="text-lg font-semibold text-gray-700">{p.label}</span>
            </button>
          );
        })}
      </div>
    </div>
  );

  const renderTaskViewer = () => {
    if (isLoading) {
      return <LoadingSpinner size="lg" text="正在获取任务..." />;
    }

    if (error) {
        const errorMessage = (error as any)?.response?.data?.message || '加载任务失败，请稍后再试。';
        return (
            <div className="text-center text-red-500">
                <p>{errorMessage}</p>
                <button onClick={handleNextTask} className="mt-4 px-4 py-2 border rounded-md">重试</button>
      </div>
    );
  }

    if (!currentTask) {
  return (
        <div className="text-center text-gray-600">
          <p>该平台暂时没有可用的任务了。</p>
           <button onClick={() => setSelectedPlatform(null)} className="mt-4 px-4 py-2 border rounded-md">返回选择平台</button>
        </div>
      );
    }

    const TaskTypeIcon = getTaskTypeIcon(currentTask.task_type);
    const platformInfo = getPlatformInfo(currentTask.platform);
    const PlatformIcon = platformInfo?.icon;
    const progress = (currentTask.completed_count / currentTask.total_quota) * 100;

    return (
      <div className="w-full max-w-2xl mx-auto animate-fade-in bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="p-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">{currentTask.title}</h2>
            <div className="flex items-center text-yellow-600 font-semibold text-lg">
                <Coins className="w-6 h-6 mr-1" />
                {currentTask.reward_points}
        </div>
      </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500 pt-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${platformInfo?.color}`}>
              {PlatformIcon && <PlatformIcon className="w-4 h-4 mr-1" />} {platformInfo?.label}
            </span>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              <TaskTypeIcon className="w-3 h-3 mr-1" />
              {taskTypeOptions.find(opt => opt.value === currentTask.task_type)?.label}
            </span>
          </div>
        </div>
        <div className="px-6 py-4 space-y-4 border-t border-gray-200">
          <div className="flex items-center bg-gray-50 p-4 rounded-lg border border-gray-200">
            <TaskTypeIcon className="w-8 h-8 text-blue-600 mr-4 flex-shrink-0" />
              <div>
              <h4 className="font-semibold text-gray-800">
                任务目标: {taskTypeOptions.find(opt => opt.value === currentTask.task_type)?.label}
              </h4>
              <p className="text-gray-600 text-sm">{currentTask.description}</p>
            </div>
      </div>

          <div>
                <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                  <span>完成进度</span>
              <span>{currentTask.completed_count}/{currentTask.total_quota}</span>
                </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                className="bg-blue-600 h-2.5 rounded-full transition-all"
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>

          <div className="flex items-center text-sm text-gray-500">
            <Clock className="w-4 h-4 mr-2" />
            <span>剩余时间: {formatTimeRemaining(currentTask.expires_at)}</span>
                </div>
              </div>
        <div className="px-6 py-4 bg-gray-50 flex justify-between items-center">
            <button onClick={() => setSelectedPlatform(null)} className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100">
                返回选择平台
            </button>
          <div className="flex gap-4">
            <button onClick={handleNextTask} disabled={isCompleting} className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 flex items-center disabled:opacity-50">
              <RefreshCw className="w-4 h-4 mr-2" />
              下一个任务
            </button>
                <button
              onClick={() => handleExecuteTask(currentTask)} 
              disabled={isCompleting}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 flex items-center disabled:opacity-50 disabled:bg-blue-400"
                >
              {isCompleting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  立即执行
                </>
              )}
                </button>
              </div>
            </div>
      </div>
    );
  };

  return (
    <div className="space-y-6 p-4 sm:p-6 lg:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">任务大厅</h1>
          <p className="text-gray-600 mt-1">
            {selectedPlatform 
                ? `正在执行 ${getPlatformInfo(selectedPlatform)?.label} 平台任务`
                : '选择感兴趣的平台，赚取积分奖励'}
          </p>
        </div>
        {selectedPlatform && (
            <button onClick={() => setSelectedPlatform(null)} className="text-blue-600 hover:underline">切换平台</button>
      )}
      </div>

      <div className="mt-8 flex items-center justify-center">
        {!selectedPlatform ? renderPlatformSelector() : renderTaskViewer()}
      </div>
    </div>
  );
};

export default TaskHallPage;
