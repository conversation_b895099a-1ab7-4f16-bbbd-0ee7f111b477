import { ApiClient } from './api';
import { Task, TaskFilters, CreateTaskForm } from '@/types';

export class TaskService {
  /**
   * 获取任务列表
   */
  static async getTasks(filters?: TaskFilters, page = 1, limit = 20) {
    const params = new URLSearchParams();
    
    if (filters?.platform) {
      params.append('platform', filters.platform);
    }
    if (filters?.task_type) {
      params.append('task_type', filters.task_type);
    }
    if (filters?.min_points) {
      params.append('min_points', filters.min_points.toString());
    }
    if (filters?.max_points) {
      params.append('max_points', filters.max_points.toString());
    }
    
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    
    const response = await ApiClient.get<{
      data: Task[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>(`/tasks?${params.toString()}`);
    return response.data;
  }

  /**
   * 获取任务详情
   */
  static async getTaskById(id: number) {
    return ApiClient.get<Task>(`/tasks/${id}`);
  }

  /**
   * 创建任务
   */
  static async createTask(data: CreateTaskForm) {
    return ApiClient.post<Task>('/tasks', data);
  }

  /**
   * 更新任务
   */
  static async updateTask(id: number, data: Partial<CreateTaskForm>) {
    return ApiClient.put<Task>(`/tasks/${id}`, data);
  }

  /**
   * 删除任务
   */
  static async deleteTask(id: number) {
    return ApiClient.delete(`/tasks/${id}`);
  }

  /**
   * 获取我发布的任务
   */
  static async getMyPublishedTasks(page = 1, limit = 20) {
    const response = await ApiClient.get<{
      data: Task[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>(`/tasks/my/published?page=${page}&limit=${limit}`);
    return response.data;
  }

  /**
   * 获取我参与的任务
   */
  static async getMyParticipatedTasks(page = 1, limit = 20) {
    const response = await ApiClient.get<{
      data: Task[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>(`/tasks/my/participated?page=${page}&limit=${limit}`);
    return response.data;
  }

  /**
   * 获取随机任务
   */
  static async getRandomTask(platform: string): Promise<Task | null> {
    try {
      const response = await ApiClient.get<Task>(`/tasks/random?platform=${platform}`);
      return response.data || null;
    } catch (error: any) {
      // 在404时，我们不希望抛出错误，而是返回null
      if (error.response && error.response.status === 404) {
        return null;
      }
      // 对于其他错误，仍然抛出
      throw error;
    }
  }

  /**
   * 参与任务
   */
  static async participateTask(taskId: number, proof?: string[]) {
    return ApiClient.post(`/tasks/${taskId}/participate`, { proof });
  }

  /**
   * 暂停任务
   */
  static async pauseTask(id: number) {
    return ApiClient.patch<Task>(`/tasks/${id}/pause`);
  }

  /**
   * 恢复任务
   */
  static async resumeTask(id: number) {
    return ApiClient.patch<Task>(`/tasks/${id}/resume`);
  }

  /**
   * 完成任务
   */
  static async completeTask(id: number) {
    return ApiClient.patch<Task>(`/tasks/${id}/complete`);
  }
}