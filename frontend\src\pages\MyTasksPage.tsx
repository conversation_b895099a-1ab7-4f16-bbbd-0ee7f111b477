import React, { useState } from 'react';
import { useQuery } from 'react-query';
import {
  Calendar,
  Users,
  Eye,
  Edit,
  Trash2,
  Plus,
  Filter,
  Search,
  MoreHorizontal,
  TrendingUp,
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Task, TaskStatus } from '@/types';
import { useAuthStore } from '@/store/authStore';
import { TaskService } from '@/services/taskService';
import LoadingSpinner from '@/components/LoadingSpinner';

type TabType = 'published' | 'participated';
type FilterType = 'all' | 'active' | 'completed' | 'expired';

const MyTasksPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [activeTab, setActiveTab] = useState<TabType>('published');
  const [filter, setFilter] = useState<FilterType>('all');
  const [searchQuery, setSearchQuery] = useState('');

  // 获取我发布的任务
  const { data: publishedTasks, isLoading: publishedLoading } = useQuery(
    ['my-published-tasks', user?.id],
    async () => {
      return await TaskService.getMyPublishedTasks();
    },
    {
      enabled: !!user,
      staleTime: 5 * 60 * 1000, // 5分钟
    }
  );

  // 获取我参与的任务
  const { data: participatedTasks, isLoading: participatedLoading } = useQuery(
    ['my-participated-tasks', user?.id],
    async () => {
      return await TaskService.getMyParticipatedTasks();
    },
    {
      enabled: !!user,
      staleTime: 2 * 60 * 1000, // 2分钟
    }
  );

  const publishedTasksData = publishedTasks?.data || [];
  const participatedTasksData = participatedTasks?.data || [];

  // 获取当前显示的任务列表
  const getCurrentTasks = () => {
    const tasks = activeTab === 'published' ? publishedTasksData : participatedTasksData;
    if (!tasks) return [];

    let filteredTasks = tasks;

    // 状态过滤
    if (filter !== 'all') {
      filteredTasks = tasks.filter((task: Task) => task.status === filter);
    }

    // 搜索过滤
    if (searchQuery) {
      filteredTasks = filteredTasks.filter((task: Task) =>
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (task.description && task.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    return filteredTasks;
  };

  // 获取状态样式
  const getStatusStyle = (status: TaskStatus) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取状态文本
  const getStatusText = (status: TaskStatus) => {
    switch (status) {
      case 'active':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'expired':
        return '已过期';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  };

  // 获取平台样式
  const getPlatformStyle = (platform: string) => {
    switch (platform) {
      case 'douyin':
        return 'bg-red-500';
      case 'kuaishou':
        return 'bg-orange-500';
      case 'xiaohongshu':
        return 'bg-pink-500';
      default:
        return 'bg-gray-500';
    }
  };

  // 获取平台名称
  const getPlatformName = (platform: string) => {
    switch (platform) {
      case 'douyin':
        return '抖音';
      case 'kuaishou':
        return '快手';
      case 'xiaohongshu':
        return '小红书';
      default:
        return platform;
    }
  };

  // 获取任务类型文本
  const getTaskTypeText = (taskType: string) => {
    switch (taskType) {
      case 'like':
        return '点赞';
      case 'share':
        return '分享';
      case 'follow':
        return '关注';
      case 'comment':
        return '评论';
      default:
        return taskType;
    }
  };

  // 计算进度百分比
  const getProgress = (completed: number, total: number) => {
    return Math.round((completed / total) * 100);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const currentTasks = getCurrentTasks();
  const isLoading = activeTab === 'published' ? publishedLoading : participatedLoading;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">我的任务</h1>
          <p className="text-gray-600 mt-1">管理我发布的任务和参与的任务</p>
        </div>
        <button
          onClick={() => navigate('/tasks/publish')}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>发布任务</span>
        </button>
      </div>

      {/* 标签页 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('published')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'published'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            我发布的
            {publishedTasksData && (
              <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                {publishedTasksData.length}
              </span>
            )}
          </button>
          <button
            onClick={() => setActiveTab('participated')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'participated'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            我参与的
            {participatedTasksData && (
              <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                {participatedTasksData.length}
              </span>
            )}
          </button>
        </nav>
      </div>

      {/* 过滤和搜索 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="搜索任务标题或描述..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as FilterType)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">全部状态</option>
            <option value="active">进行中</option>
            <option value="completed">已完成</option>
            <option value="expired">已过期</option>
          </select>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner />
          </div>
        ) : currentTasks.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-4xl">📝</span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery || filter !== 'all' ? '没有找到匹配的任务' : '暂无任务'}
            </h3>
            <p className="text-gray-500 mb-4">
              {activeTab === 'published'
                ? '您还没有发布任何任务'
                : '您还没有参与任何任务'}
            </p>
            {activeTab === 'published' && (
              <button
                onClick={() => navigate('/tasks/publish')}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                发布第一个任务
              </button>
            )}
          </div>
        ) : (
          currentTasks.map((task: Task) => (
            <div key={task.id} className="card hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{task.title}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusStyle(task.status)}`}>
                      {getStatusText(task.status)}
                    </span>
                    <div className="flex items-center space-x-1">
                      <div className={`w-3 h-3 ${getPlatformStyle(task.platform)} rounded-full`}></div>
                      <span className="text-sm text-gray-600">{getPlatformName(task.platform)}</span>
                      <span className="text-sm text-gray-400">·</span>
                      <span className="text-sm text-gray-600">{getTaskTypeText(task.task_type)}</span>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-3 line-clamp-2">{task.description}</p>
                  
                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{task.completed_count}/{task.total_quota}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="w-4 h-4" />
                      <span>{getProgress(task.completed_count, task.total_quota)}%</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>截止 {formatDate(task.expires_at)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-blue-600 font-medium">{task.reward_points} 积分</span>
                    </div>
                  </div>
                  
                  {/* 进度条 */}
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all ${
                          task.status === 'completed'
                            ? 'bg-green-500'
                            : task.status === 'expired'
                            ? 'bg-red-500'
                            : 'bg-blue-500'
                        }`}
                        style={{ width: `${getProgress(task.completed_count, task.total_quota)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
                
                {/* 操作按钮 */}
                <div className="flex items-center space-x-2 ml-4">
                  <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <Eye className="w-4 h-4" />
                  </button>
                  {activeTab === 'published' && (
                    <>
                      <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </>
                  )}
                  <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <MoreHorizontal className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default MyTasksPage;
