import axios from 'axios';

// 视频平台类型
export enum VideoPlatform {
  DOUYIN = 'douyin',
  KUAISHOU = 'kuaishou',
  XIAOHONGSHU = 'xiaohong<PERSON>',
  BILIBILI = 'bilibili',
  YOUTUBE = 'youtube',
  TIKTOK = 'tiktok',
  UNKNOWN = 'unknown'
}

// 视频信息接口
export interface VideoInfo {
  platform: VideoPlatform;
  title: string;
  cover: string;
  author: string;
  duration?: number;
  description?: string;
  videoId: string;
  originalUrl: string;
}

// 平台URL模式
const PLATFORM_PATTERNS = {
  [VideoPlatform.DOUYIN]: [
    /(?:https?:\/\/)?(?:www\.)?douyin\.com\/video\/(\d+)/,
    /(?:https?:\/\/)?(?:www\.)?iesdouyin\.com\/share\/video\/(\d+)/,
    /(?:https?:\/\/)?v\.douyin\.com\/([A-Za-z0-9]+)/,
    /(?:https?:\/\/)?(?:www\.)?douyin\.com\/note\/(\d+)/,
    /(?:https?:\/\/)?(?:www\.)?douyin\.com\/.*\?recommend=([A-Za-z0-9]+)/
  ],
  [VideoPlatform.KUAISHOU]: [
    /(?:https?:\/\/)?(?:www\.)?kuaishou\.com\/short-video\/(\d+)/,
    /(?:https?:\/\/)?v\.kuaishou\.com\/([A-Za-z0-9]+)/,
    /(?:https?:\/\/)?(?:www\.)?kuaishou\.com\/profile\/[^\/]+\/video\/(\d+)/
  ],
  [VideoPlatform.XIAOHONGSHU]: [
    /(?:https?:\/\/)?(?:www\.)?xiaohongshu\.com\/explore\/([A-Za-z0-9]+)/,
    /(?:https?:\/\/)?(?:www\.)?xiaohongshu\.com\/discovery\/item\/([A-Za-z0-9]+)/,
    /(?:https?:\/\/)?xhslink\.com\/([A-Za-z0-9]+)/
  ],
  [VideoPlatform.BILIBILI]: [
    /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(BV[A-Za-z0-9]+)/,
    /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(av\d+)/,
    /(?:https?:\/\/)?b23\.tv\/([A-Za-z0-9]+)/
  ],
  [VideoPlatform.YOUTUBE]: [
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([A-Za-z0-9_-]+)/,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/shorts\/([A-Za-z0-9_-]+)/,
    /(?:https?:\/\/)?youtu\.be\/([A-Za-z0-9_-]+)/
  ],
  [VideoPlatform.TIKTOK]: [
    /(?:https?:\/\/)?(?:www\.)?tiktok\.com\/@[^\/]+\/video\/(\d+)/,
    /(?:https?:\/\/)?vm\.tiktok\.com\/([A-Za-z0-9]+)/,
    /(?:https?:\/\/)?(?:www\.)?tiktok\.com\/t\/([A-Za-z0-9]+)/
  ]
};

/**
 * 识别视频平台
 */
export function detectVideoPlatform(url: string): VideoPlatform {
  for (const [platform, patterns] of Object.entries(PLATFORM_PATTERNS)) {
    for (const pattern of patterns) {
      if (pattern.test(url)) {
        return platform as VideoPlatform;
      }
    }
  }
  return VideoPlatform.UNKNOWN;
}

/**
 * 提取视频ID
 */
export function extractVideoId(url: string, platform: VideoPlatform): string | null {
  const patterns = PLATFORM_PATTERNS[platform];
  if (!patterns) return null;

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }
  return null;
}

/**
 * 获取视频信息
 */
export async function getVideoInfo(url: string): Promise<VideoInfo> {
  try {
    // 检测平台
    const platform = detectVideoPlatform(url);
    if (platform === VideoPlatform.UNKNOWN) {
      throw new Error('不支持的视频平台');
    }

    // 提取视频ID
    const videoId = extractVideoId(url, platform);
    if (!videoId) {
      throw new Error('无法提取视频ID');
    }

    // 调用后端API获取视频信息
    const response = await axios.post('/api/video/parse', {
      url,
      platform,
      videoId
    });

    return response.data;
  } catch (error) {
    console.error('获取视频信息失败:', error);
    
    // 返回默认信息
    const platform = detectVideoPlatform(url);
    const videoId = extractVideoId(url, platform) || 'unknown';
    
    return {
      platform,
      title: '无法获取标题',
      cover: '/placeholder-cover.jpg',
      author: '未知作者',
      description: '',
      videoId,
      originalUrl: url
    };
  }
}

/**
 * 模拟获取视频信息（用于演示）
 */
export async function getMockVideoInfo(url: string): Promise<VideoInfo> {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  const platform = detectVideoPlatform(url);
  const videoId = extractVideoId(url, platform) || 'mock_id';

  // 根据平台返回模拟数据
  const mockData: Record<VideoPlatform, Partial<VideoInfo>> = {
    [VideoPlatform.DOUYIN]: {
      title: '超火的抖音视频标题 #热门 #推荐',
      cover: 'https://picsum.photos/400/600?random=1',
      author: '抖音达人',
      description: '这是一个很有趣的抖音视频，快来点赞吧！',
      duration: 15
    },
    [VideoPlatform.KUAISHOU]: {
      title: '快手精彩视频 - 记录美好生活',
      cover: 'https://picsum.photos/400/600?random=2',
      author: '快手用户',
      description: '快手短视频，记录生活的美好瞬间',
      duration: 30
    },
    [VideoPlatform.XIAOHONGSHU]: {
      title: '小红书种草笔记 | 好物推荐',
      cover: 'https://picsum.photos/400/600?random=3',
      author: '小红书博主',
      description: '分享好物，种草必备！',
      duration: 60
    },
    [VideoPlatform.BILIBILI]: {
      title: 'B站UP主精彩视频 - 知识分享',
      cover: 'https://picsum.photos/400/600?random=4',
      author: 'B站UP主',
      description: '优质内容，值得一看！',
      duration: 300
    },
    [VideoPlatform.YOUTUBE]: {
      title: 'Amazing YouTube Video - Tutorial',
      cover: 'https://picsum.photos/400/600?random=5',
      author: 'YouTuber',
      description: 'Great content from YouTube!',
      duration: 600
    },
    [VideoPlatform.TIKTOK]: {
      title: 'Viral TikTok Video #fyp',
      cover: 'https://picsum.photos/400/600?random=6',
      author: 'TikToker',
      description: 'Trending TikTok content!',
      duration: 30
    },
    [VideoPlatform.UNKNOWN]: {
      title: '未知平台视频',
      cover: 'https://picsum.photos/400/600?random=7',
      author: '未知作者',
      description: '无法识别的视频平台',
      duration: 0
    }
  };

  const platformData = mockData[platform] || mockData[VideoPlatform.UNKNOWN];

  return {
    platform,
    videoId,
    originalUrl: url,
    title: platformData.title || '未知标题',
    cover: platformData.cover || '/placeholder-cover.jpg',
    author: platformData.author || '未知作者',
    description: platformData.description || '',
    duration: platformData.duration || 0
  };
}

/**
 * 获取平台显示名称
 */
export function getPlatformDisplayName(platform: VideoPlatform): string {
  const names = {
    [VideoPlatform.DOUYIN]: '抖音',
    [VideoPlatform.KUAISHOU]: '快手',
    [VideoPlatform.XIAOHONGSHU]: '小红书',
    [VideoPlatform.BILIBILI]: 'B站',
    [VideoPlatform.YOUTUBE]: 'YouTube',
    [VideoPlatform.TIKTOK]: 'TikTok',
    [VideoPlatform.UNKNOWN]: '未知平台'
  };
  return names[platform] || '未知平台';
}

/**
 * 获取平台图标
 */
export function getPlatformIcon(platform: VideoPlatform): string {
  const icons = {
    [VideoPlatform.DOUYIN]: '🎵',
    [VideoPlatform.KUAISHOU]: '⚡',
    [VideoPlatform.XIAOHONGSHU]: '📖',
    [VideoPlatform.BILIBILI]: '📺',
    [VideoPlatform.YOUTUBE]: '📹',
    [VideoPlatform.TIKTOK]: '🎬',
    [VideoPlatform.UNKNOWN]: '❓'
  };
  return icons[platform] || '❓';
}

/**
 * 验证视频URL格式
 */
export function isValidVideoUrl(url: string): boolean {
  if (!url || typeof url !== 'string') return false;
  
  // 基本URL格式检查
  try {
    new URL(url);
  } catch {
    return false;
  }

  // 检查是否匹配支持的平台
  return detectVideoPlatform(url) !== VideoPlatform.UNKNOWN;
}
